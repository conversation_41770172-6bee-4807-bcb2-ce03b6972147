// Base database models
export interface Account {
  id: string;
  name: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface Campaign {
  id: string;
  account_id: string;
  name: string;
  status: string;
  objective: string;
  created_at: string;
  updated_at: string;
}

export interface AdSet {
  id: string;
  campaign_id: string;
  account_id: string;
  name: string;
  status: string;
  targeting: any;
  budget_remaining: number;
  daily_budget: number;
  lifetime_budget: number;
  created_at: string;
  updated_at: string;
}

export interface Video {
  id: string;
  source?: string;
  created_at: string;
  updated_at: string;
}

export interface Ad {
  id: string;
  adset_id: string;
  campaign_id: string;
  account_id: string;
  name: string;
  status: string;
  video_id?: string;
  video?: Video;
  creative: AdCreative;
  created_at: string;
  updated_at: string;
}

export interface AdCreative {
  image_url?: string;
  video_url?: string;
  title?: string;
  body?: string;
  call_to_action?: string;
  link_url?: string;
  thumbnail_url?: string;
  object_story_spec?: {
    page_id?: string;
    instagram_actor_id?: string;
    video_data?: {
      video_id?: string;
      image_url?: string;
      video_url?: string;
      call_to_action?: any;
      message?: string;
      title?: string;
      description?: string;
    };
    photo_data?: {
      image_url?: string;
      call_to_action?: any;
      message?: string;
      url?: string;
    };
    link_data?: {
      image_url?: string;
      video_id?: string;
      call_to_action?: any;
      description?: string;
      link?: string;
      message?: string;
      name?: string;
      picture?: string;
    };
    template_data?: {
      call_to_action?: any;
      format_option?: string;
      message?: string;
      name?: string;
      template_url_spec?: any;
    };
  };
}

export interface AdInsightsWeekly {
  id: string;
  ad_id: string;
  adset_id: string;
  campaign_id: string;
  account_id: string;
  week_start: string;
  week_end: string;

  // Basic metrics
  total_impressions: number;
  total_spend: number;
  total_reach: number;
  avg_cpm: number;
  avg_frequency: number;

  // Conversion metrics
  total_purchases: number;
  total_purchase_value: number;
  cpa: number;
  roas: number;

  // Engagement metrics
  total_landing_page_views: number;
  cost_per_landing_page_view: number;
  total_outbound_clicks: number;
  unique_outbound_clicks: number;
  outbound_ctr: number;
  total_video_plays_3s: number;
  total_add_to_carts: number;
  cost_per_add_to_cart: number;

  // Advanced metrics
  hook_rate: number;
  hold_rate: number;
  conversion_rate: number;
  average_order_value: number;
  traffic_quality: number;

  created_at: string;
  updated_at: string;
}

export interface WeeklyPeriod {
  week_start: string;
  week_end: string;
}

// Extended models with relationships
export interface AdWithDetails extends Ad {
  adset?: AdSet;
  campaign?: Campaign;
  account?: Account;
  insights?: AdInsightsWeekly[];
}

export interface AdWinner extends AdWithDetails {
  performance_score: number;
  rank_in_adset: number;
  best_metric: string;
  best_metric_value: number;
  weekly_insights: AdInsightsWeekly;
}

// Filter and query types
export interface AdWinnerFilters {
  account_id?: string;
  campaign_id?: string;
  adset_id?: string;
  week_start?: string;
  week_end?: string;
  metric?: AdWinnerMetric;
  min_spend?: number;
  min_impressions?: number;
}

export type AdWinnerMetric =
  | 'roas'
  | 'conversion_rate'
  | 'hook_rate'
  | 'hold_rate'
  | 'traffic_quality'
  | 'cpa'
  | 'avg_cpm'
  | 'outbound_ctr';

export interface AdWinnerResponse {
  data: AdWinner[];
  total_count: number;
  filters_applied: AdWinnerFilters;
  periods_available: WeeklyPeriod[];
}

// UI specific types
export interface FilterOption {
  label: string;
  value: string;
}

export interface MetricOption {
  label: string;
  value: AdWinnerMetric;
  description: string;
  format: 'percentage' | 'currency' | 'number';
}

export interface AdWinnerTableColumn {
  field: string;
  header: string;
  sortable: boolean;
  filterable: boolean;
  format?: 'currency' | 'percentage' | 'number' | 'date';
}

// Constants
export const AD_WINNER_METRICS: MetricOption[] = [
  {
    label: 'ROAS',
    value: 'roas',
    description: 'Return on Ad Spend',
    format: 'number',
  },
  {
    label: 'Conversion Rate',
    value: 'conversion_rate',
    description: 'Purchase rate (purchases / landing page views)',
    format: 'percentage',
  },
  {
    label: 'Hook Rate',
    value: 'hook_rate',
    description: 'Video view rate (3-second plays / impressions)',
    format: 'percentage',
  },
  {
    label: 'Hold Rate',
    value: 'hold_rate',
    description: 'Video retention rate (viewers who watched beyond hook)',
    format: 'percentage',
  },
  {
    label: 'Traffic Quality',
    value: 'traffic_quality',
    description:
      'Landing page view rate (landing page views / outbound clicks)',
    format: 'percentage',
  },
  {
    label: 'CPA',
    value: 'cpa',
    description: 'Cost per Acquisition',
    format: 'currency',
  },
  {
    label: 'CPM',
    value: 'avg_cpm',
    description: 'Cost per Mille (thousand impressions)',
    format: 'currency',
  },
  {
    label: 'CTR',
    value: 'outbound_ctr',
    description: 'Click-through Rate',
    format: 'percentage',
  },
];

export const AD_WINNER_TABLE_COLUMNS: AdWinnerTableColumn[] = [
  {
    field: 'creative.image_url',
    header: 'Creative',
    sortable: false,
    filterable: false,
  },
  { field: 'name', header: 'Ad Name', sortable: true, filterable: true },
  { field: 'adset.name', header: 'Ad Set', sortable: true, filterable: true },
  {
    field: 'campaign.name',
    header: 'Campaign',
    sortable: true,
    filterable: true,
  },
  {
    field: 'performance_score',
    header: 'Score',
    sortable: true,
    filterable: false,
    format: 'number',
  },
  {
    field: 'weekly_insights.roas',
    header: 'ROAS',
    sortable: true,
    filterable: false,
    format: 'number',
  },
  {
    field: 'weekly_insights.total_spend',
    header: 'Spend',
    sortable: true,
    filterable: false,
    format: 'currency',
  },
  {
    field: 'weekly_insights.conversion_rate',
    header: 'Conv. Rate',
    sortable: true,
    filterable: false,
    format: 'percentage',
  },
  {
    field: 'weekly_insights.hook_rate',
    header: 'Hook Rate',
    sortable: true,
    filterable: false,
    format: 'percentage',
  },
  {
    field: 'weekly_insights.hold_rate',
    header: 'Hold Rate',
    sortable: true,
    filterable: false,
    format: 'percentage',
  },
  {
    field: 'rank_in_adset',
    header: 'Rank',
    sortable: true,
    filterable: false,
    format: 'number',
  },
];
